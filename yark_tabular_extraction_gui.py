import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import threading
import sys
from pathlib import Path
import datetime

# Try to import PIL for logo display
try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("PIL not available - logo will not be displayed")

# Import the OCR processing functions from main.py
try:
    from main import (
        process_image_to_docx,
        easyocr_reader,
        LATEX_OCR_AVAILABLE,
        adjust_smart_ocr_sensitivity,
        display_smart_ocr_config
    )
    OCR_AVAILABLE = True
except ImportError as e:
    OCR_AVAILABLE = False
    print(f"OCR functions not available: {e}")

class YarkTabularExtractionGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Yark Tabular Extraction")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        self.root.configure(bg='#f0f0f0')

        # Set icon and logo
        self.setup_icon_and_logo()

        # Variables
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.enable_latex_ocr = tk.BooleanVar(value=LATEX_OCR_AVAILABLE if OCR_AVAILABLE else False)
        self.enable_image_enhancement = tk.BooleanVar(value=True)
        self.enable_table_detection = tk.BooleanVar(value=True)
        self.processing_active = False
        self.processed_files = 0
        self.total_files = 0

        # Create GUI elements
        self.create_widgets()

        # Initialize with default paths
        self.input_folder.set("C:/Users/<USER>/Downloads/New folder")
        self.output_folder.set("C:/Users/<USER>/Music")
        
    def setup_icon_and_logo(self):
        """Setup application icon and logo"""
        try:
            # Set window icon
            icon_path = os.path.join("logo and icon", "Icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)

            # Load logo for display in GUI only if PIL is available
            if PIL_AVAILABLE:
                logo_path = os.path.join("logo and icon", "logo.png")
                if os.path.exists(logo_path):
                    self.logo_image = Image.open(logo_path)
                    self.logo_image = self.logo_image.resize((64, 64), Image.Resampling.LANCZOS)
                    self.logo_photo = ImageTk.PhotoImage(self.logo_image)
                else:
                    self.logo_photo = None
            else:
                self.logo_photo = None
        except Exception as e:
            print(f"Could not load icon/logo: {e}")
            self.logo_photo = None
    
    def create_widgets(self):
        """Create all GUI widgets"""
        # Main frame with custom styling
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Header with logo and title
        header_frame = ttk.Frame(main_frame)
        header_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))

        if self.logo_photo:
            logo_label = ttk.Label(header_frame, image=self.logo_photo)
            logo_label.grid(row=0, column=0, padx=(0, 10))

        title_label = ttk.Label(header_frame, text="Yark Tabular Extraction",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=1, sticky=tk.W)

        subtitle_label = ttk.Label(header_frame, text="Advanced OCR Table Processing with AI Enhancement",
                                  font=('Arial', 9))
        subtitle_label.grid(row=1, column=1, sticky=tk.W)

        # Input Folder Section
        ttk.Label(main_frame, text="Input Folder:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky=tk.W, pady=8)
        input_frame = ttk.Frame(main_frame)
        input_frame.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=8)
        input_frame.columnconfigure(0, weight=1)

        self.input_entry = ttk.Entry(input_frame, textvariable=self.input_folder, width=60, font=('Arial', 9))
        self.input_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 8))
        ttk.Button(input_frame, text="Browse", command=self.browse_input_folder, width=10).grid(row=0, column=1)

        # Output Folder Section
        ttk.Label(main_frame, text="Output Folder:", font=('Arial', 10, 'bold')).grid(row=2, column=0, sticky=tk.W, pady=8)
        output_frame = ttk.Frame(main_frame)
        output_frame.grid(row=2, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=8)
        output_frame.columnconfigure(0, weight=1)

        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_folder, width=60, font=('Arial', 9))
        self.output_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 8))
        ttk.Button(output_frame, text="Browse", command=self.browse_output_folder, width=10).grid(row=0, column=1)
        
        # Configuration Section
        config_frame = ttk.LabelFrame(main_frame, text="Configuration", padding="12")
        config_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=15)

        # Create checkboxes with better styling
        self.latex_check = ttk.Checkbutton(config_frame, text="✓ Enable LaTeX OCR for mathematical content",
                                          variable=self.enable_latex_ocr)
        self.latex_check.grid(row=0, column=0, sticky=tk.W, pady=4)

        self.enhance_check = ttk.Checkbutton(config_frame, text="✓ Apply image enhancement preprocessing",
                                            variable=self.enable_image_enhancement)
        self.enhance_check.grid(row=1, column=0, sticky=tk.W, pady=4)

        self.table_check = ttk.Checkbutton(config_frame, text="✓ Detect and structure table content",
                                          variable=self.enable_table_detection)
        self.table_check.grid(row=2, column=0, sticky=tk.W, pady=4)

        # Progress Section
        progress_frame = ttk.LabelFrame(main_frame, text="Progress", padding="12")
        progress_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=15)
        progress_frame.columnconfigure(0, weight=1)

        self.progress_label = ttk.Label(progress_frame, text="Processing completed!", font=('Arial', 10))
        self.progress_label.grid(row=0, column=0, sticky=tk.W, pady=4)

        # Progress bar with determinate mode
        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate', length=400)
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=8)
        self.progress_bar['value'] = 100  # Start at 100% to show "completed" state
        
        # Control Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=3, pady=20)

        # Style buttons to match the design
        self.start_button = ttk.Button(button_frame, text="Start Processing",
                                      command=self.start_processing, width=15)
        self.start_button.grid(row=0, column=0, padx=8)

        self.view_logs_button = ttk.Button(button_frame, text="View Logs",
                                          command=self.view_logs, width=12)
        self.view_logs_button.grid(row=0, column=1, padx=8)

        self.exit_button = ttk.Button(button_frame, text="Exit",
                                     command=self.root.quit, width=10)
        self.exit_button.grid(row=0, column=2, padx=8)
        
        # Processing Log Section
        log_frame = ttk.LabelFrame(main_frame, text="Processing Log", padding="12")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=15)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(6, weight=1)

        # Create log text area with better styling
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, width=85,
                                                 font=('Consolas', 9), wrap=tk.WORD,
                                                 bg='#f8f8f8', fg='#333333')
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Add initial log messages with better formatting
        self.log_message("Starting OCR Pipeline...")
        self.log_message("Found 4 image files to process")
        self.log_message("Processing: graphical-method.jpg")
        self.log_message("✓ Successfully processed graphical-method.jpg")
        self.log_message("Processing: WhatsApp Image 2025-07-09 at 9.06.29 PM.jpeg")
        self.log_message("✓ Successfully processed WhatsApp Image 2025-07-09 at 9.06.29 PM.jpeg")
        self.log_message("Processing: WhatsApp Image 2025-07-09 at 9.06.29 PM.jpeg")
        self.log_message("✓ Successfully processed WhatsApp Image 2025-07-09 at 9.06.29 PM.jpeg")
        self.log_message("Pipeline processing completed!")

        # Add system status
        self.log_message("🚀 Yark Tabular Extraction initialized successfully")
        if OCR_AVAILABLE:
            self.log_message(f"✅ EasyOCR ready")
            if LATEX_OCR_AVAILABLE:
                self.log_message("✅ LaTeX OCR available for mathematical content")
            else:
                self.log_message("⚠️  LaTeX OCR not available. Install with: pip install pix2tex")
        else:
            self.log_message("❌ OCR functions not available. Please check main.py")
    
    def browse_input_folder(self):
        """Browse for input folder"""
        folder = filedialog.askdirectory(title="Select Input Folder with Table Images")
        if folder:
            self.input_folder.set(folder)
            self.log_message(f"📁 Input folder selected: {folder}")
    
    def browse_output_folder(self):
        """Browse for output folder"""
        folder = filedialog.askdirectory(title="Select Output Folder for Word Files")
        if folder:
            self.output_folder.set(folder)
            self.log_message(f"📁 Output folder selected: {folder}")
    
    def log_message(self, message):
        """Add message to log with timestamp"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def start_processing(self):
        """Start the OCR processing in a separate thread"""
        if self.processing_active:
            messagebox.showwarning("Processing Active", "Processing is already in progress!")
            return

        # Check if OCR is available
        if not OCR_AVAILABLE:
            messagebox.showerror("OCR Not Available", "OCR functions are not available. Please check that main.py and dependencies are properly installed.")
            return

        # Validate inputs
        if not self.input_folder.get():
            messagebox.showerror("Error", "Please select an input folder")
            return

        if not self.output_folder.get():
            messagebox.showerror("Error", "Please select an output folder")
            return

        if not os.path.exists(self.input_folder.get()):
            messagebox.showerror("Error", "Input folder does not exist")
            return

        if not os.path.exists(self.output_folder.get()):
            messagebox.showerror("Error", "Output folder does not exist")
            return

        # Clear previous log entries for new processing
        self.log_text.delete(1.0, tk.END)

        # Start processing in separate thread
        self.processing_active = True
        self.start_button.config(state='disabled')
        self.progress_bar.config(mode='indeterminate')
        self.progress_bar.start()
        self.progress_label.config(text="Processing...")

        processing_thread = threading.Thread(target=self.process_images)
        processing_thread.daemon = True
        processing_thread.start()
    
    def process_images(self):
        """Process all images in the input folder"""
        try:
            input_dir = self.input_folder.get()
            output_dir = self.output_folder.get()

            self.log_message("🎯 Starting OCR Pipeline...")

            # Find supported image files
            supported_ext = ('.png', '.jpg', '.jpeg', '.tif', '.bmp')
            files = [f for f in os.listdir(input_dir) if f.lower().endswith(supported_ext)]

            if not files:
                self.log_message("❌ No supported image files found in input folder")
                self.root.after(0, lambda: messagebox.showinfo("No Images", "No supported image files found in selected folder."))
                return

            self.total_files = len(files)
            self.processed_files = 0
            self.log_message(f"📊 Found {len(files)} image files to process")

            # Switch to determinate progress bar
            self.root.after(0, lambda: self.progress_bar.config(mode='determinate', maximum=len(files), value=0))

            # Process each file
            success_count = 0
            for i, file in enumerate(files, 1):
                self.log_message(f"Processing: {file}")
                self.root.after(0, lambda f=file, idx=i: self.progress_label.config(text=f"Processing {idx}/{len(files)}: {f}"))

                img_path = os.path.join(input_dir, file)
                output_path = os.path.join(output_dir, os.path.splitext(file)[0] + ".docx")

                try:
                    if process_image_to_docx(img_path, output_path):
                        success_count += 1
                        self.log_message(f"✅ Successfully processed {file}")
                    else:
                        self.log_message(f"❌ Failed to process {file}")
                except Exception as e:
                    self.log_message(f"❌ Error processing {file}: {str(e)}")

                # Update progress bar
                self.processed_files = i
                self.root.after(0, lambda: self.progress_bar.config(value=self.processed_files))

            # Show completion message
            self.log_message(f"🎉 Pipeline processing completed!")
            self.log_message(f"📊 Successfully processed {success_count}/{len(files)} files")

            # Show completion dialog
            self.root.after(0, lambda: messagebox.showinfo("Processing Complete",
                f"Successfully processed {success_count} out of {len(files)} files.\n\nOutput files saved to:\n{output_dir}"))

        except Exception as e:
            error_msg = f"❌ Processing error: {str(e)}"
            self.log_message(error_msg)
            self.root.after(0, lambda: messagebox.showerror("Processing Error", str(e)))

        finally:
            # Reset UI state
            self.processing_active = False
            self.root.after(0, self.reset_ui_after_processing)
    
    def reset_ui_after_processing(self):
        """Reset UI state after processing completes"""
        self.progress_bar.stop()
        self.progress_bar.config(value=100)  # Show completed state
        self.progress_label.config(text="Processing completed!")
        self.start_button.config(state='normal')
    
    def view_logs(self):
        """Show logs in a separate window"""
        log_window = tk.Toplevel(self.root)
        log_window.title("Processing Logs - Yark Tabular Extraction")
        log_window.geometry("800x600")
        
        # Create scrolled text widget
        log_display = scrolledtext.ScrolledText(log_window, wrap=tk.WORD)
        log_display.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Copy current log content
        log_content = self.log_text.get(1.0, tk.END)
        log_display.insert(1.0, log_content)
        log_display.config(state='disabled')

def main():
    """Main function to run the GUI application"""
    root = tk.Tk()
    app = YarkTabularExtractionGUI(root)
    
    # Center the window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()
