# Yark Tabular Extraction

**Advanced OCR Table Extraction Software with AI-Enhanced Mathematical Content Processing**

## Overview

Yark Tabular Extraction is a powerful desktop application that converts table images into properly formatted Word documents. It features intelligent OCR processing with specialized mathematical content recognition, making it perfect for extracting complex tables containing equations, formulas, and mathematical expressions.

## Key Features

### 🧠 Smart OCR Technology
- **Dual OCR Engine**: Combines EasyOCR for general text with LaTeX OCR for mathematical content
- **Intelligent Cell Analysis**: Automatically determines the best OCR method based on cell size and content type
- **Adaptive Processing**: Adjusts OCR sensitivity based on table complexity

### 📊 Advanced Table Processing
- **Structure Detection**: Automatically detects and preserves table structure
- **Mathematical Content**: Specialized handling for equations, fractions, and mathematical expressions
- **Multi-format Support**: Processes PNG, JPG, JPEG, TIF, and BMP image formats

### 🎯 User-Friendly Interface
- **Modern GUI**: Clean, intuitive interface with real-time progress tracking
- **Batch Processing**: Process multiple table images simultaneously
- **Live Logging**: Real-time processing feedback and detailed logs
- **Configuration Options**: Customizable OCR settings and processing options

## System Requirements

- **Operating System**: Windows 10/11
- **Python**: 3.7 or higher
- **Memory**: 4GB RAM minimum (8GB recommended for large batches)
- **Storage**: 2GB free space for dependencies and processing

## Installation

### Quick Start
1. Download and extract the Yark Tabular Extraction package
2. Double-click `install_dependencies.bat` to install required packages
3. Run `run_yark_tabular_extraction.bat` to start the application

### Manual Installation
```bash
# Install required Python packages
pip install -r requirements.txt

# Optional: Install LaTeX OCR for enhanced mathematical content
pip install pix2tex

# Run the application
python yark_tabular_extraction_gui.py
```

## Usage Guide

### Basic Operation
1. **Launch Application**: Run `run_yark_tabular_extraction.bat`
2. **Select Input Folder**: Choose folder containing your table images
3. **Select Output Folder**: Choose where to save the Word documents
4. **Configure Settings**: Enable desired processing options
5. **Start Processing**: Click "Start Processing" to begin extraction

### Configuration Options
- **Enable LaTeX OCR**: Use AI-powered mathematical content recognition
- **Apply Image Enhancement**: Improve image quality before OCR processing
- **Detect Table Structure**: Automatically organize content into proper table format

### Supported File Types
- **Input**: PNG, JPG, JPEG, TIF, BMP image files
- **Output**: Microsoft Word (.docx) documents with formatted tables

## Advanced Features

### Mathematical Content Processing
- Automatic detection of mathematical expressions
- LaTeX to Word equation conversion
- Proper formatting of fractions, subscripts, and superscripts
- Greek letter and mathematical symbol recognition

### Intelligent Table Analysis
- Grid-based cell mapping for precise content placement
- Content-aware column assignment
- Automatic ratio and equation detection
- Smart error correction for common OCR mistakes

### Batch Processing
- Process hundreds of images automatically
- Progress tracking with detailed logging
- Error handling and recovery
- Comprehensive processing reports

## File Structure

```
Yark Tabular Extraction/
├── yark_tabular_extraction_gui.py    # Main GUI application
├── main.py                           # Core OCR processing engine
├── run_yark_tabular_extraction.bat   # Application launcher
├── install_dependencies.bat          # Dependency installer
├── requirements.txt                  # Python dependencies
├── logo and icon/                    # Application branding
│   ├── Icon.ico                     # Application icon
│   └── logo.png                     # Application logo
└── README_YARK_TABULAR_EXTRACTION.md # This documentation
```

## Troubleshooting

### Common Issues

**"Python not found" Error**
- Install Python 3.7+ from python.org
- Ensure Python is added to system PATH

**"Import Error" Messages**
- Run `install_dependencies.bat` as administrator
- Manually install missing packages: `pip install package_name`

**Poor OCR Accuracy**
- Ensure images are high resolution (minimum 300 DPI)
- Use clear, well-lit table images
- Enable image enhancement preprocessing

**LaTeX OCR Not Working**
- Install LaTeX OCR: `pip install pix2tex`
- Ensure sufficient system memory (8GB+ recommended)

### Performance Optimization
- Close other applications during large batch processing
- Use SSD storage for faster file operations
- Process images in smaller batches for better stability

## Technical Specifications

### OCR Engines
- **EasyOCR**: General text recognition with 80+ language support
- **LaTeX OCR**: Specialized mathematical expression recognition
- **Smart Routing**: Automatic selection based on content analysis

### Image Processing
- **Preprocessing**: Adaptive contrast enhancement, noise reduction, sharpening
- **Scaling**: Automatic upscaling for small images
- **Format Support**: All major image formats with color/grayscale processing

### Output Quality
- **Table Structure**: Preserved grid layout with proper cell alignment
- **Mathematical Formatting**: Equations in proper Word format
- **Text Quality**: High-accuracy OCR with error correction

## Support and Updates

For technical support, feature requests, or bug reports, please refer to the application logs available through the "View Logs" button in the GUI.

## Version Information

**Current Version**: 2.0.0 (Yark Tabular Extraction)
**Release Date**: 2025-07-12
**Compatibility**: Windows 10/11, Python 3.7+

---

*Yark Tabular Extraction - Transforming table images into professional Word documents with AI-powered precision.*
